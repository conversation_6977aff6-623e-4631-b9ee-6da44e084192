"""
Data management and memory optimization for trading bot
"""

import pandas as pd
import numpy as np
from datetime import datetime
import copy
from typing import Dict, Any, Optional, List
import gc
import logging

from .config import *

logger = logging.getLogger(__name__)
pd.options.mode.copy_on_write = True

class DataManager:
    """Manages market data with memory optimization"""

    def __init__(self, trade_mode: str = "backtest"):
        self.df = {}
        self.recent_level = {}
        self.primary_level = {}
        self.sub_primary_level = {}
        self.waves = {}
        self.past_ranges = {}
        self.position_status = {}
        self.pending_position = {}
        self.closed_position = {}
        self.cancelled_position = {}
        self.trade_mode = trade_mode
        self.strategy_name = None

        # Memory optimization counters
        self._candle_count = 0
        self._last_cleanup = 0

        self.history = {}

        # Initialize data structures
        self._initialize_data_structures()

    def _initialize_data_structures(self):
        """Initialize data structures for all symbols and timeframes"""
        for name in CHECK_SYMBOLS.keys():
            self.df[name] = {}
            self.recent_level[name] = {}
            self.primary_level[name] = {}
            self.sub_primary_level[name] = {}
            self.history[name] = {}
            self.position_status[name] = {}
            self.pending_position[name] = {}
            self.closed_position[name] = {}
            self.cancelled_position[name] = {}
            self.waves[name] = {}
            self.past_ranges[name] = {}

            for timeframe in TIMEFRAMES:
                self._initialize_timeframe_data(name, timeframe)

    def _initialize_timeframe_data(self, name: str, timeframe: int):
        """Initialize data for specific symbol and timeframe"""
        self.recent_level[name][timeframe] = None
        self.primary_level[name][timeframe] = None
        self.sub_primary_level[name][timeframe] = None
        self.waves[name][timeframe] = []
        self.past_ranges[name][timeframe] = []
        self.position_status[name][timeframe] = []
        self.pending_position[name][timeframe] = []
        self.closed_position[name][timeframe] = []
        self.cancelled_position[name][timeframe] = []
        self.history[name][timeframe] = {
            'primary_level': [],
            'recent_level': [],
            'sub_primary_level': [],
        }

    def load_data(self, name: str, timeframe: int = None, limit_rows: int = MAX_ROW_DF, initialize_waves: bool = True) -> bool:
        """Load data from CSV files with memory optimization"""
        try:
            if timeframe == 1:
                file_name = f"{DATA_DIR}/{name}_{timeframe}_{self.trade_mode}.csv"
                df_temp = pd.read_csv(
                    file_name,
                    sep=',',
                    parse_dates=['Open time', 'Close time'],
                    index_col='Open time'
                )
                # Keep only recent data to save memory
                self.df[name][timeframe] = df_temp.tail(limit_rows)
            else:
                # Resample from 1-minute data
                self.df[name][timeframe] = self.df[name][1].resample(f'{timeframe}min').agg({
                    'Open': 'first',
                    'High': 'max',
                    'Low': 'min',
                    'Close': 'last',
                    'Volume': 'sum',
                })

            # Add technical indicators
            self._add_technical_indicators(name, timeframe)

            # Init first primary level and wave
            if initialize_waves:
                self.waves[name][timeframe] = []
                logger.debug(f"No waves data for {name} {timeframe}, initializing basic wave")
                # Initialize with a basic wave structure
                self.waves[name][timeframe].append({
                    'start_index': self.df[name][timeframe].index[0],
                    'checked_index': self.df[name][timeframe].index[0],
                    'peak_index': self.df[name][timeframe].index[0],
                    'confirmed': True,
                    'label': 1,  # Default to up wave
                    'ranges': [],
                })

                # Initialize primary level
                self.primary_level[name][timeframe] = {
                        'secondary_key_index': self.df[name][timeframe].index[0],
                        'key_level_index': self.df[name][timeframe].index[0],
                        'start_index': self.df[name][timeframe].index[0],
                        'checked_index': self.df[name][timeframe].index[0],
                        'peak_index': self.df[name][timeframe].index[0],
                        'label': 1,
                        'type_wave': 'wave',
                        'stack_level': 0,
                        'start_stack_level': 0,
                        'false_break_level_index': None,
                        'false_break_peak_index': None,
                        'old_peak_index': None,
                        'old_key_level_index': None,
                    }

                self.history[name][timeframe]['primary_level'] = []
                self.history[name][timeframe]['primary_level'].append(copy.deepcopy(self.primary_level[name][timeframe]))
            return True

        except Exception as e:
            logger.error(f"Error loading data for {name} {timeframe}: {e}")
            return False

    def _add_technical_indicators(self, name: str, timeframe: int):
        """Add technical indicators to dataframe"""
        df = self.df[name][timeframe]

        # Basic indicators
        df['candle_size'] = abs(df['Open'] - df['Close'])
        df['Labels'] = (df['Close'] > df['Open']).astype(int)
        df['label'] = df['Labels']
        df['ma_candle'] = None

        # Ichimoku components
        df['span_a'] = float('nan')
        df['span_b'] = float('nan')
        df['tenkan'] = float('nan')
        df['kijun'] = float('nan')
        df['chikou'] = float('nan')

    def add_new_candle(self, name: str, timeframe: int, candle_data: Dict[str, Any]):
        """Add new candle data with memory management"""
        try:

            latest_candle = self.df[name][timeframe].iloc[-1]
            if candle_data['Open time'] < latest_candle.name:
                logger.debug(f"New candle time {candle_data['Open time']} is before latest candle time {latest_candle.name} for {name} {timeframe}")
                return False
            if candle_data['Open time'] == latest_candle.name:
                # Overwrite latest candle
                new_candle_data = {
                    'Open': candle_data['Open'],
                    'High': candle_data['High'],
                    'Low': candle_data['Low'],
                    'Close': candle_data['Close'],
                    'Volume': candle_data['Volume'],
                    'Close time': candle_data['Close time'],
                }
                self.df[name][timeframe].iloc[-1] = new_candle_data
                return True

            # Convert to pandas Series
            new_row = pd.Series(candle_data, name=candle_data['Open time'])

            # Add to dataframe
            self.df[name][timeframe] = pd.concat([
                self.df[name][timeframe],
                new_row.to_frame().T
            ])

            # Memory cleanup
            self._candle_count += 1
            if self._candle_count - self._last_cleanup >= CLEANUP_INTERVAL:
                self._cleanup_memory(name, timeframe)
                self._last_cleanup = self._candle_count

            return True

        except Exception as e:
            logger.error(f"Error adding candle for {name} {timeframe}: {e}")
            return False

    def _cleanup_memory(self, name: str, timeframe: int):
        """Clean up memory by removing old data"""
        try:
            # Keep only recent data
            if len(self.df[name][timeframe]) > MAX_ROW_DF:
                self.df[name][timeframe] = self.df[name][timeframe].tail(MAX_ROW_DF)

            # Limit history size
            for key in self.history:
                if len(self.history[key]) > MAX_HISTORY_SIZE:
                    self.history[key] = self.history[key][-MAX_HISTORY_SIZE:]

            # Limit waves history
            if len(self.waves[name][timeframe]) > MAX_HISTORY_SIZE:
                self.waves[name][timeframe] = self.waves[name][timeframe][-MAX_HISTORY_SIZE:]

            # Force garbage collection
            gc.collect()

            logger.info(f"Memory cleanup completed for {name} {timeframe}")

        except Exception as e:
            logger.error(f"Error during memory cleanup: {e}")

    def get_dataframe(self, name: str, timeframe: int) -> Optional[pd.DataFrame]:
        """Get dataframe for symbol and timeframe"""
        try:
            return self.df[name][timeframe]
        except KeyError:
            return None

    def get_latest_candle(self, name: str, timeframe: int) -> Optional[pd.Series]:
        """Get latest candle for symbol and timeframe"""
        try:
            return self.df[name][timeframe].iloc[-1]
        except (KeyError, IndexError):
            return None

    def update_ma_candle(self, name: str, timeframe: int, index: int):
        """Update moving average candle size"""
        try:
            df = self.df[name][timeframe]
            if index - NUMBER_CAL_MA_CANDLE < 0:
                df.iloc[index, df.columns.get_loc('ma_candle')] = df.iloc[index]['candle_size']
            else:
                start_idx = max(0, index - NUMBER_CAL_MA_CANDLE)
                ma_value = df.iloc[start_idx:index+1]['candle_size'].sum() / NUMBER_CAL_MA_CANDLE
                df.iloc[index, df.columns.get_loc('ma_candle')] = ma_value

        except Exception as e:
            logger.error(f"Error updating MA candle: {e}")

    def get_memory_usage(self) -> Dict[str, Any]:
        """Get current memory usage statistics"""
        memory_info = {}

        for name in CHECK_SYMBOLS:
            memory_info[name] = {}
            for timeframe in TIMEFRAMES:
                if name in self.df and timeframe in self.df[name]:
                    df_memory = self.df[name][timeframe].memory_usage(deep=True).sum()
                    memory_info[name][timeframe] = {
                        'rows': len(self.df[name][timeframe]),
                        'memory_mb': df_memory / (1024 * 1024),
                        'waves_count': len(self.waves[name][timeframe])
                    }

        return memory_info

    def update_data_to_new_timestamp(self, symbol: str, timestamp: Any, timeframe: int):
        """Update data manager with level and trend status for new timestamp"""
        try:
            # Get the row data for the timestamp
            df = self.df[symbol][timeframe]
            if df is None or timestamp not in df.index:
                logger.debug(f"Timestamp {timestamp} not found in dataframe for {symbol} {timeframe}")
                return

            row = df.loc[timestamp]
            if row is None:
                return

            logger.debug(f"Update level and trend status for {symbol} and timeframe: {timeframe}m")

            # Get current data structures
            waves = self.waves[symbol][timeframe]
            history = self.history[symbol][timeframe]


            if not self.recent_level[symbol][timeframe]:
                self.recent_level[symbol][timeframe] = copy.deepcopy(self.primary_level[symbol][timeframe])

            if not self.sub_primary_level[symbol][timeframe]:
                self.sub_primary_level[symbol][timeframe] = copy.deepcopy(self.primary_level[symbol][timeframe])

            # Get previous checked index
            previous_checked_index = waves[-1]['checked_index']

            if timestamp <= previous_checked_index:
                logger.debug(f"Timestamp {timestamp} is before previous checked index {previous_checked_index} for {symbol} {timeframe}")
                return

            # Process all rows from previous checked index to current timestamp
            try:
                start_loc = df.index.get_loc(previous_checked_index)
                end_loc = df.index.get_loc(timestamp)

                if end_loc <= start_loc:
                    return

                # Process each row in the range
                for i in range(start_loc + 1, end_loc + 1):
                    local_row = df.iloc[i]

                    # Update waves with new row
                    logger.debug('--update waves--')
                    self._update_waves_with_new_row(df, waves, local_row)

                    # Update primary level
                    logger.debug('--update primary level--')

                    # Find start index for level calculation
                    start_index = self._find_peak_index_wave_candle_belongs_to(
                        self.primary_level[symbol][timeframe]['peak_index'], waves, self.primary_level[symbol][timeframe]['label']
                    )
                    if start_index is None:
                        start_index = 0

                    # Update primary level from waves
                    updated_primary_level = self._level_from_waves(
                        df, waves[start_index:], True, self.primary_level[symbol][timeframe]
                    )

                    # Find wave index to start from key primary
                    wave_index_start_from_key_primary = self._find_peak_index_wave_candle_belongs_to(
                        updated_primary_level['key_level_index'], waves, 1 - self.primary_level[symbol][timeframe]['label']
                    )
                    if wave_index_start_from_key_primary is None:
                        wave_index_start_from_key_primary = len(waves) - 1
                    else:
                        wave_index_start_from_key_primary = wave_index_start_from_key_primary + 1

                    # Ensure levels are valid
                    updated_recent_level = None
                    updated_sub_primary_level = None

                    # Check if primary level changed significantly
                    if updated_primary_level['key_level_index'] != self.primary_level[symbol][timeframe]['key_level_index']:
                        logger.info(f"Primary level changed for {symbol} {timeframe}")

                        # Add to history and reset sub levels
                        self.history[symbol][timeframe]['primary_level'].append(copy.deepcopy(updated_primary_level))
                        self.history[symbol][timeframe]['recent_level'] = []
                        self.history[symbol][timeframe]['sub_primary_level'] = []

                        # Create new levels
                        if len(waves[wave_index_start_from_key_primary:]) > 0:
                            updated_recent_level = self._level_from_waves(
                                df, waves[wave_index_start_from_key_primary:], False
                            )
                            updated_sub_primary_level = self._level_from_waves(
                                df, waves[wave_index_start_from_key_primary:], True
                            )

                        # Plot chart df and level if new primary level change
                        from .chart_generator import ChartGenerator
                        chart_generator = ChartGenerator(self)
                        chart_path = chart_generator.plot_position_analysis(symbol, 15, None)
                        if chart_path:
                            logger.info(f"Chart saved after primary level change: {chart_path}")
                    else:
                        # Update existing levels
                        wave_index_start_from_recent_level = self._find_peak_index_wave_candle_belongs_to(
                            self.recent_level[symbol][timeframe]['peak_index'], waves, self.recent_level[symbol][timeframe]['label']
                        )
                        updated_recent_level = self._level_from_waves(
                            df, waves[wave_index_start_from_recent_level:], False, self.recent_level[symbol][timeframe]
                        )

                        wave_index_start_from_sub_primary_level = self._find_peak_index_wave_candle_belongs_to(
                            self.sub_primary_level[symbol][timeframe]['peak_index'], waves, self.sub_primary_level[symbol][timeframe]['label']
                        )
                        updated_sub_primary_level = self._level_from_waves(
                            df, waves[wave_index_start_from_sub_primary_level:], True, self.sub_primary_level[symbol][timeframe]
                        )

                        # from .chart_generator import ChartGenerator
                        # chart_generator = ChartGenerator(self)
                        # chart_generator.plot_position_analysis(symbol, 15, None)

                        # Update history if levels changed while primary level not changed
                        if (updated_recent_level and len(self.history[symbol][timeframe]['recent_level']) == 0 or \
                        updated_recent_level['key_level_index'] != self.recent_level[symbol][timeframe]['key_level_index']):
                            self.history[symbol][timeframe]['recent_level'].append(copy.deepcopy(updated_recent_level))

                        if (updated_sub_primary_level and len(self.history[symbol][timeframe]['sub_primary_level']) == 0 or \
                        updated_sub_primary_level['key_level_index'] != self.sub_primary_level[symbol][timeframe]['key_level_index']):
                            self.history[symbol][timeframe]['sub_primary_level'].append(copy.deepcopy(updated_sub_primary_level))
                    # Update the data manager with new levels
                    self.primary_level[symbol][timeframe] = updated_primary_level
                    self.recent_level[symbol][timeframe] = updated_recent_level
                    self.sub_primary_level[symbol][timeframe] = updated_sub_primary_level



            except (KeyError, IndexError) as e:
                logger.error(f"Error processing timestamp range: {e}")
                return

        except Exception as e:
            logger.error(f"Error updating data manager: {e}")

    def _update_waves_with_new_row(self, df: pd.DataFrame, waves: List[Dict], new_row: pd.Series):
        """Update waves with new row data"""
        try:
            # Import the actual function from wave_analysis
            from .wave_analysis import update_waves_with_new_row_df
            update_waves_with_new_row_df(df, waves, new_row)
        except Exception as e:
            logger.error(f"Error updating waves: {e}")

    def _find_peak_index_wave_candle_belongs_to(self, candle_index: Any, waves: List[Dict], label_wave: int) -> Optional[int]:
        """Find peak index wave that candle belongs to"""
        check_index = -1
        while True:
            if waves[check_index]['start_index'] <= candle_index and waves[check_index]['checked_index'] >= candle_index and waves[check_index]['label'] == label_wave:
                return check_index
            check_index = check_index - 1
            if check_index*-1 > len(waves):
                return None

    def _level_from_waves(self, df: pd.DataFrame, waves: List[Dict], primary_key: bool = False,
                         init_level: Optional[Dict] = None) -> Dict[str, Any]:
        """Generate level from waves"""
        try:
            # Import the actual function from level_analysis
            from .level_analysis import level_from_waves
            return level_from_waves(df, waves, primary_key, init_level)
        except Exception as e:
            logger.error(f"Error generating level from waves: {e}")
            return init_level or {}

    def update_higher_timeframe_candle(self, symbol: str, timeframe: int,
                                     latest_1m_candle: pd.Series, latest_1m_time: Any):
        """
        Update higher timeframe candle incrementally instead of resampling all data

        Args:a
            symbol: Trading symbol
            timeframe: Target timeframe (5, 15, 60, 1440)
            latest_1m_candle: Latest 1-minute candle data
            latest_1m_time: Latest 1-minute candle timestamp
        """
        try:
            # Get current higher timeframe data
            df_higher = self.get_dataframe(symbol, timeframe)
            df_1m = self.get_dataframe(symbol, 1)

            # Next candle start time
            next_candle_start_in_higher_tf = df_higher.index[-1] + pd.Timedelta(minutes=timeframe)
            next_candle_end_in_higher_tf = next_candle_start_in_higher_tf + pd.Timedelta(minutes=timeframe - 1)

            if latest_1m_time < next_candle_start_in_higher_tf:
                logger.debug(f"Latest 1m time {latest_1m_time} is before next candle start {next_candle_start_in_higher_tf} for {symbol} {timeframe}")
                return
            if latest_1m_time < next_candle_end_in_higher_tf:
                logger.debug(f"Latest 1m time {latest_1m_time} is within next candle {next_candle_start_in_higher_tf} - {next_candle_end_in_higher_tf} for {symbol} {timeframe}")
                return

            # Calculate the timeframe boundary for the latest 1m candle
            # Round down to the nearest timeframe boundary
            timeframe_start = next_candle_start_in_higher_tf

            # Create new candle (new timeframe period)
            new_candle = {
                'Open': df_1m.loc[timeframe_start]['Open'],
                'High': df_1m.loc[timeframe_start:latest_1m_time]['High'].max(),
                'Low': df_1m.loc[timeframe_start:latest_1m_time]['Low'].min(),
                'Close': latest_1m_candle['Close'],
                'Volume': df_1m.loc[timeframe_start:latest_1m_time]['Volume'].sum(),
            }

            # Add new row to dataframe
            new_row = pd.Series(new_candle, name=timeframe_start)
            self.df[symbol][timeframe] = pd.concat([
                self.df[symbol][timeframe],
                new_row.to_frame().T
            ])

            # Update technical indicators
            self._add_technical_indicators(symbol, timeframe)

            # If timeframe_start < last_higher_time, it means we're getting old data
            # which shouldn't happen in live trading, so we ignore it

        except Exception as e:
            logger.error(f"Error updating higher timeframe {timeframe} for {symbol}: {e}")
